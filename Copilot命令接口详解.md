# OpenRA Copilot 命令接口详解

本文档详细分析 OpenRA Copilot 提供的所有命令接口、可读取的游戏数据，以及鼠标键盘交互支持情况。

## 📋 完整命令接口列表

### 🎮 控制类命令 (CommandHandlers)

| 命令名称 | 功能描述 | 主要参数 | 返回值 |
|---------|----------|----------|--------|
| **move_actor** | 移动单位到指定位置 | targets, location, attack_move | 执行状态消息 |
| **camera_move** | 移动摄像机视角 | location | 执行状态消息 |
| **select_unit** | 选择指定单位 | targets | 执行状态消息 |
| **form_group** | 编组单位 | targets, group_id | 执行状态消息 |
| **attack** | 攻击指定目标 | attackers, targets | 执行状态消息 |
| **deploy** | 部署单位 | targets | 执行状态消息 |
| **view** | 查看指定位置 | location | 执行状态消息 |
| **occupy** | 占领建筑 | targets, building | 执行状态消息 |
| **repair** | 修理单位/建筑 | targets | 执行状态消息 |
| **stop** | 停止单位行动 | targets | 执行状态消息 |
| **set_rally_point** | 设置集结点 | building, location | 执行状态消息 |
| **place_building** | 放置建筑 | building_type, location | 执行状态消息 |
| **manage_production** | 管理生产队列 | action, queue_id, item | 执行状态消息 |

### 🔍 查询类命令 (QueryHandlers)

| 命令名称 | 功能描述 | 主要参数 | 返回数据结构 |
|---------|----------|----------|-------------|
| **query_actor** | 查询单位信息 | targets, range, faction | 单位详细信息数组 |
| **player_baseinfo_query** | 查询玩家基础信息 | 无 | 资源、电力、人口等 |
| **map_query** | 查询地图信息 | 无 | 地图尺寸、资源点等 |
| **fog_query** | 查询战争迷雾状态 | pos | 可见性和探索状态 |
| **query_production_queue** | 查询生产队列 | queue_type | 生产队列详细信息 |
| **query_wait_info** | 查询等待信息 | wait_id | 等待任务状态 |
| **query_path** | 查询路径信息 | targets, destination | 路径规划结果 |
| **query_can_produce** | 查询生产能力 | units | 是否可生产指定单位 |
| **unit_attribute_query** | 查询单位属性 | targets | 单位详细属性信息 |
| **screen_info_query** | 查询屏幕信息 | 无 | 当前视野范围信息 |
| **start_production** | 开始生产 | units, auto_place | 生产任务信息 |
| **ping** | 心跳检测 | 无 | 服务器状态 |

---

## 📊 可读取的游戏数据详解

### 1. 玩家基础信息 (player_baseinfo_query)

```json
{
  "Cash": 5000,              // 当前金钱
  "Resources": 0,            // 当前资源
  "Power": 50,               // 净电力 (提供 - 消耗)
  "PowerDrained": 100,       // 电力消耗
  "PowerProvided": 150,      // 电力提供
  "Population": 25,          // 当前人口
  "PopulationCap": 50        // 人口上限
}
```

### 2. 单位信息 (query_actor)

```json
{
  "actors": [
    {
      "id": 12345,                    // 单位唯一ID
      "type": "1tnk",                 // 单位类型标识符
      "chinese_name": "轻坦克",        // 中文名称
      "faction": "己方",               // 阵营 (己方/敌方/中立)
      "hp": 300,                      // 当前生命值
      "maxHp": 300,                   // 最大生命值
      "isDead": false,                // 是否死亡
      "position": {                   // 位置坐标
        "x": 25,
        "y": 30
      },
      "speed": 85,                    // 移动速度
      "hasAttackRange": true,         // 是否有攻击能力
      "targets": [12346, 12347]       // 攻击范围内的目标ID
    }
  ],
  "count": 1
}
```

### 3. 地图信息 (map_query)

```json
{
  "map_info": {
    "title": "Desert Strike",        // 地图名称
    "size": {                        // 地图尺寸
      "width": 64,
      "height": 64
    },
    "bounds": {                      // 地图边界
      "left": 0,
      "top": 0,
      "right": 64,
      "bottom": 64
    },
    "theater": "desert"              // 地形类型
  },
  "resources": [                     // 资源点信息
    {
      "position": {"x": 10, "y": 15},
      "type": "ore",                 // 资源类型
      "density": 3                   // 资源密度
    }
  ],
  "spawn_points": [                  // 出生点
    {"position": {"x": 5, "y": 5}}
  ],
  "resource_count": 25
}
```

### 4. 生产队列信息 (query_production_queue)

```json
{
  "queue_type": "infantry",          // 队列类型
  "queue_items": [
    {
      "name": "e1",                  // 单位标识符
      "chineseName": "步兵",          // 中文名称
      "remaining_time": 150,         // 剩余时间(帧)
      "total_time": 300,             // 总时间(帧)
      "remaining_cost": 50,          // 剩余成本
      "total_cost": 100,             // 总成本
      "paused": false,               // 是否暂停
      "done": false,                 // 是否完成
      "progress_percent": 50,        // 完成百分比
      "owner_actor_id": 1001,        // 所属建筑ID
      "status": "in_progress"        // 状态
    }
  ],
  "has_ready_item": false
}
```

### 5. 战争迷雾信息 (fog_query)

```json
{
  "IsVisible": true,                 // 当前是否可见
  "IsExplored": true                 // 是否已探索
}
```

### 6. 路径查询信息 (query_path)

```json
{
  "path": [                          // 路径点数组
    {"x": 25, "y": 30},
    {"x": 26, "y": 30},
    {"x": 27, "y": 31}
  ],
  "distance": 150,                   // 路径总距离
  "cost": 200,                       // 路径成本
  "reachable": true                  // 是否可达
}
```

---

## 🖱️ 鼠标键盘交互支持分析

### 🎯 Agent模式配置

OpenRA Copilot 提供了 **IsAgentMode** 配置选项来控制鼠标键盘交互：

```csharp
[Desc("Copilot Agent-only mode. Disables all player control when enabled.")]
public bool IsAgentMode = false;
```

### 🔧 交互模式详解

#### 1. 混合模式 (IsAgentMode = false) - **默认推荐**

**特点：**
- ✅ **同时支持** AI Agent 和人工操作
- ✅ **实时协作** - AI和人类可以同时控制
- ✅ **灵活切换** - 可随时人工干预
- ✅ **调试友好** - 便于开发和测试

**使用场景：**
```json
// AI Agent 通过 Socket API 控制
{
  "command": "move_actor",
  "params": {
    "targets": [1001, 1002],
    "location": {"x": 50, "y": 30}
  }
}

// 同时人类玩家可以：
// - 鼠标点击选择单位
// - 键盘快捷键操作
// - 右键移动单位
// - 滚轮缩放视角
```

#### 2. 纯Agent模式 (IsAgentMode = true)

**特点：**
- ❌ **禁用人工操作** - 所有鼠标键盘输入被忽略
- ✅ **纯AI控制** - 只能通过Socket API操作
- ✅ **比赛模式** - 确保公平竞争
- ⚠️ **调试困难** - 无法人工干预

**启用方式：**
```csharp
// 1. 配置文件设置
Game.Settings.Game.IsAgentMode = true;

// 2. Lua脚本控制
Trigger.SetAgentMode(true);

// 3. 启动参数
--agent-mode=true
```

### 🎮 输入处理机制

#### 输入处理器架构
```csharp
public interface IInputHandler
{
    void ModifierKeys(Modifiers mods);     // 修饰键处理
    void OnKeyInput(KeyInput input);       // 键盘输入
    void OnMouseInput(MouseInput input);   // 鼠标输入
    void OnTextInput(string text);         // 文本输入
}

// 默认输入处理器
public class DefaultInputHandler : IInputHandler
{
    public void OnMouseInput(MouseInput input)
    {
        // 检查是否为Agent模式
        if (Game.Settings.Game.IsAgentMode)
            return; // 忽略鼠标输入
            
        Sync.RunUnsynced(world, () => Ui.HandleInput(input));
    }
}
```

#### 鼠标操作支持
```csharp
// 鼠标配置选项
public class GameSettings
{
    public bool LockMouseWindow = false;           // 锁定鼠标到窗口
    public MouseScrollType MouseScroll = MouseScrollType.Joystick;
    public MouseButtonPreference MouseButtonPreference = new();
    public bool UseClassicMouseStyle = false;      // 经典鼠标样式
    public bool UseAlternateScrollButton = false;  // 备用滚动按钮
    public int SelectionDeadzone = 24;             // 选择死区
    public int MouseScrollDeadzone = 8;            // 滚动死区
}
```

#### 键盘快捷键支持
```yaml
# 游戏内快捷键配置 (ingame.yaml)
LogicKeyListener@WORLD_KEYHANDLER:
  Logic: CycleBasesHotkeyLogic, CycleProductionActorsHotkeyLogic
  CycleBasesKey: CycleBase                    # 循环基地
  CycleProductionActorsKey: CycleProductionBuildings  # 循环生产建筑
  SelectAllUnitsKey: SelectAllUnits           # 全选单位
  PauseKey: PauseGame                         # 暂停游戏
```

### 🔄 实时协作机制

#### 命令优先级
1. **Socket API命令** - 立即执行，优先级最高
2. **鼠标操作** - 实时响应，可被API命令覆盖
3. **键盘快捷键** - 实时响应，可被API命令覆盖

#### 冲突处理
```csharp
// 示例：AI和人类同时选择单位
// 1. AI通过API选择单位1001
{
  "command": "select_unit",
  "params": {"targets": [1001]}
}

// 2. 人类鼠标点击选择单位1002
// 结果：最后的操作生效，单位1002被选中

// 3. AI再次通过API选择单位1001
// 结果：API命令覆盖鼠标操作，单位1001被选中
```

### 📱 摄像机控制

#### API控制
```json
{
  "command": "camera_move",
  "params": {
    "location": {"x": 50, "y": 30}
  }
}
```

#### 鼠标键盘控制
```csharp
// 鼠标滚轮缩放
if (mi.Event == MouseInputEvent.Scroll && mi.Modifiers.HasModifier(ZoomModifier))
{
    worldRenderer.Viewport.AdjustZoom(mi.Delta.Y * ZoomSpeed, mi.Location);
}

// 边缘滚动
if (Game.Settings.Game.ViewportEdgeScroll && Game.Renderer.WindowHasInputFocus)
{
    edgeDirections = CheckForDirections();
    // 自动滚动视角
}

// 键盘方向键滚动
keyboardDirections = keyboardDirections.Set(scrollDirection, 
    e.Event == KeyInputEvent.Down && isHotkey);
```

---

## 🎯 最佳实践建议

### 1. 开发阶段 - 混合模式
```bash
# 启动配置
IsAgentMode = false
CopilotDebug = true
CopilotPort = 7445
```

**优势：**
- 可以人工干预调试AI行为
- 实时观察AI决策效果
- 快速测试不同场景

### 2. 比赛阶段 - 纯Agent模式
```bash
# 比赛配置
IsAgentMode = true
CopilotDebug = false
CopilotPort = 7445
```

**优势：**
- 确保公平竞争
- 避免人工干预
- 纯AI能力展示

### 3. 演示阶段 - 混合模式
```bash
# 演示配置
IsAgentMode = false
CopilotDebug = false
CopilotPort = 7445
```

**优势：**
- 可以展示AI和人类协作
- 实时解释AI决策
- 互动性更强

---

## 🔧 技术实现细节

### 输入事件流
```
鼠标/键盘输入 → InputHandler → IsAgentMode检查 → UI处理 → 游戏逻辑
Socket API → CopilotServer → 命令处理器 → 游戏逻辑
```

### 并发控制
- **线程安全**: Socket API和输入处理在不同线程
- **同步机制**: 通过游戏主循环同步所有操作
- **冲突解决**: 后执行的命令覆盖前面的操作

### 性能考虑
- **输入延迟**: 鼠标键盘输入延迟 < 16ms
- **API延迟**: Socket API响应延迟 < 50ms
- **并发处理**: 支持多客户端同时连接

---

**总结**: OpenRA Copilot 提供了完整的命令接口体系，支持灵活的鼠标键盘交互配置，既可以实现纯AI控制，也可以支持人机协作，为不同应用场景提供了最佳的解决方案。

*命令接口详解完成 - 2025年8月*
