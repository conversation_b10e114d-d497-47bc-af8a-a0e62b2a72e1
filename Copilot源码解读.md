# OpenRA Copilot 源码深度解读

本文档深入解读 OpenRA Copilot 的源码架构，分析其如何在原版 OpenRA 基础上实现 AI Agent 控制接口。

## 📋 目录

1. [项目架构概览](#项目架构概览)
2. [核心组件分析](#核心组件分析)
3. [Socket API 服务器](#socket-api-服务器)
4. [命令处理系统](#命令处理系统)
5. [中文单位映射](#中文单位映射)
6. [模组配置系统](#模组配置系统)
7. [集成机制分析](#集成机制分析)

---

## 🏗️ 项目架构概览

### 核心文件结构
```
copilot-source/
├── OpenRA.Game/
│   ├── CopilotCommandServer.cs    # Socket API 服务器核心
│   ├── CopilotModels.cs          # 数据模型和验证器
│   └── World.cs                  # 游戏世界集成点
├── OpenRA.Mods.Common/
│   ├── CopilotConfig.cs          # 中文单位映射配置
│   ├── CopilotUtils.cs           # 工具函数库
│   └── ServerCommands.cs         # 命令处理器集合
└── mods/
    ├── common/Copilot.yaml       # 中文单位映射数据
    └── copilot/                  # Copilot 模组配置
```

### 技术栈分析
- **基础引擎**: OpenRA (C# + .NET 6.0)
- **网络通信**: Socket TCP (端口 7445)
- **数据格式**: JSON (Newtonsoft.Json)
- **配置系统**: YAML (MiniYaml)
- **多语言支持**: 中英文错误信息

---

## 🔧 核心组件分析

### 1. CopilotCommandServer - Socket API 服务器

这是整个 Copilot 系统的核心，负责处理外部 AI Agent 的连接和命令。

#### 核心特性
```csharp
public class CopilotCommandServer
{
    readonly Socket serverSocket;           // TCP Socket 服务器
    readonly World world;                   // 游戏世界引用
    public const string CurrentApiVersion = "1.0";
    
    // 命令处理器字典
    public Dictionary<string, CommandHandler> CommandHandlers = new();
    public Dictionary<string, QueryHandler> QueryHandlers = new();
    
    // 多语言错误信息支持
    static readonly Dictionary<string, Dictionary<string, string>> ErrorMessages;
}
```

#### 启动机制
```csharp
public void Start()
{
    serverSocket.Bind(new IPEndPoint(IPAddress.Any, port));
    serverSocket.Listen(10);
    isRunning = true;
    
    // 异步处理客户端连接
    _ = Task.Run(async () =>
    {
        while (isRunning)
        {
            var clientSocket = await serverSocket.AcceptAsync();
            HandleClient(clientSocket);  // 处理每个客户端
        }
    });
}
```

#### 请求处理流程
```csharp
async void HandleClient(Socket clientSocket)
{
    // 1. 接收 JSON 数据
    var buffer = new byte[16384];
    var received = await clientSocket.ReceiveAsync(buffer, SocketFlags.None);
    var jsonString = Encoding.UTF8.GetString(buffer, 0, received);
    
    // 2. 解析请求
    MCPRequest request = JsonConvert.DeserializeObject<MCPRequest>(jsonString);
    
    // 3. 验证请求
    var (isValid, validationError) = MCPValidator.ValidateRequest(request);
    
    // 4. 路由到对应处理器
    if (CommandHandlers.TryGetValue(request.Command, out var commandHandler))
    {
        var result = commandHandler?.Invoke(request.Params, world);
        SendSuccessResponse(clientSocket, result, request.RequestId);
    }
}
```

### 2. CopilotModels - 数据模型系统

定义了完整的请求/响应数据结构和验证逻辑。

#### 请求模型
```csharp
public class MCPRequest
{
    [JsonProperty("apiVersion", Required = Required.Always)]
    public string ApiVersion { get; set; }
    
    [JsonProperty("requestId", Required = Required.Always)]
    public string RequestId { get; set; }
    
    [JsonProperty("command", Required = Required.Always)]
    public string Command { get; set; }
    
    [JsonProperty("params")]
    public JObject Params { get; set; }
    
    [JsonProperty("language")]
    public string Language { get; set; } = "zh";  // 默认中文
}
```

#### 响应模型
```csharp
public class MCPResponse
{
    [JsonProperty("status")]
    public int Status { get; set; }           // 1=成功, -1=失败
    
    [JsonProperty("requestId")]
    public string RequestId { get; set; }
    
    [JsonProperty("response")]
    public string Response { get; set; }      // 成功消息
    
    [JsonProperty("data")]
    public JObject Data { get; set; }         // 返回数据
    
    [JsonProperty("error")]
    public MCPError Error { get; set; }       // 错误信息
}
```

#### 参数验证器
```csharp
public static class MCPValidator
{
    public static (bool isValid, MCPError error) ValidateRequest(MCPRequest request)
    {
        // 基础验证
        if (request == null) return (false, new MCPError { ... });
        if (string.IsNullOrEmpty(request.Command)) return (false, new MCPError { ... });
        
        return (true, null);
    }
    
    public static (bool isValid, MCPError error) ValidateCommandParams(string command, JObject parameters)
    {
        // 根据命令类型验证参数
        switch (command)
        {
            case "move_actor": return ValidateMoveActorParams(parameters);
            case "attack": return ValidateAttackParams(parameters);
            default: return (true, null);
        }
    }
}
```

### 3. 与游戏世界的集成

#### World.cs 中的集成点
```csharp
internal World(string mapUID, ModData modData, OrderManager orderManager, WorldType type)
{
    // ... 其他初始化代码 ...
    
    // 只在常规游戏模式下启动 Copilot 服务器
    if (Type == WorldType.Regular)
    {
        CopilotServer = new CopilotCommandServer(gameSettings.CopilotPort, this);
        CopilotServer.DebugMode = gameSettings.CopilotDebug;
        CopilotServer.Start();
    }
}
```

#### 配置参数 (Settings.cs)
```csharp
public class GameSettings
{
    [Desc("Port number for the Copilot command server.")]
    public int CopilotPort = 7445;
    
    [Desc("Enable debug mode for Copilot command server.")]
    public bool CopilotDebug = false;
    
    [Desc("Copilot Agent-only mode. Disables all player control when enabled.")]
    public bool IsAgentMode = false;
}
```

---

## 🎮 命令处理系统

### ServerCommands.cs - 命令处理器集合

这个文件包含了所有 Socket API 命令的具体实现，是连接外部 AI 和游戏内部逻辑的桥梁。

#### 命令注册机制
```csharp
public void WorldLoaded(World w, WorldRenderer wr)
{
    if (w.Type == WorldType.Regular && w.CopilotServer != null)
    {
        // 注册命令处理器
        w.CopilotServer.CommandHandlers["move_actor"] = MoveActorCommand;
        w.CopilotServer.CommandHandlers["attack"] = AttackCommand;
        w.CopilotServer.CommandHandlers["select_unit"] = SelectUnitCommand;
        w.CopilotServer.CommandHandlers["form_group"] = FormGroupCommand;
        w.CopilotServer.CommandHandlers["deploy"] = DeployCommand;
        w.CopilotServer.CommandHandlers["place_building"] = PlaceBuildingCommand;
        
        // 注册查询处理器
        w.CopilotServer.QueryHandlers["query_actor"] = ActorQueryCommand;
        w.CopilotServer.QueryHandlers["player_baseinfo_query"] = PlayerBaseInfoQueryCommand;
        w.CopilotServer.QueryHandlers["map_query"] = MapQueryCommand;
        w.CopilotServer.QueryHandlers["ping"] = PingCommand;
        
        // 初始化配置
        CopilotsConfig.LoadConfig();
        CopilotsUtils.WaitInit();
    }
}
```

#### 典型命令实现示例

**移动单位命令**:
```csharp
string MoveActorCommand(JObject json, World world)
{
    // 1. 解析参数
    var targets = ParseTargets(json["targets"], world);
    var location = ParseLocation(json, world);
    
    // 2. 验证参数
    if (!targets.Any()) return "未找到目标单位";
    
    // 3. 执行游戏逻辑
    foreach (var actor in targets)
    {
        if (location.HasValue)
        {
            var order = new Order("Move", actor, Target.FromCell(world, location.Value), false);
            world.IssueOrder(order);
        }
    }
    
    return "移动命令已发送";
}
```

**查询玩家信息**:
```csharp
JObject PlayerBaseInfoQueryCommand(JObject json, World world)
{
    var player = world.LocalPlayer ?? world.Players.FirstOrDefault(p => p.Playable);
    if (player == null) return new JObject { ["error"] = "未找到玩家" };
    
    var playerResources = player.PlayerActor.Trait<PlayerResources>();
    var powerManager = player.PlayerActor.Trait<PowerManager>();
    
    return new JObject
    {
        ["Cash"] = playerResources.Cash,
        ["Resources"] = playerResources.Resources,
        ["Power"] = powerManager.PowerProvided - powerManager.PowerDrained,
        ["PowerDrained"] = powerManager.PowerDrained,
        ["PowerProvided"] = powerManager.PowerProvided
    };
}
```

### 命令分类

#### 1. 控制类命令 (CommandHandlers)
- **move_actor**: 移动单位
- **attack**: 攻击目标
- **select_unit**: 选择单位
- **form_group**: 编组单位
- **deploy**: 部署单位
- **stop**: 停止单位
- **place_building**: 放置建筑
- **manage_production**: 管理生产队列

#### 2. 查询类命令 (QueryHandlers)
- **query_actor**: 查询单位信息
- **player_baseinfo_query**: 查询玩家基础信息
- **map_query**: 查询地图信息
- **fog_query**: 查询战争迷雾
- **query_production_queue**: 查询生产队列
- **ping**: 心跳检测

---

## 🌏 中文单位映射

### CopilotConfig.cs - 配置加载器

实现了中文单位名称到游戏内部标识符的双向映射。

#### 配置加载机制
```csharp
public static void LoadConfig()
{
    // 1. 定位配置文件
    var filePath = Path.Combine(parentDirectory, "mods", "common", "Copilot.yaml");
    
    // 2. 解析 YAML 配置
    var yamlNodes = MiniYaml.FromFile(filePath);
    var unitsNode = yamlNodes.FirstOrDefault(node => node.Key == "units")?.Value;
    
    // 3. 构建双向映射字典
    configNameToChinese = new Dictionary<string, List<string>>();
    chineseToConfigName = new Dictionary<string, List<string>>();
    
    foreach (var node in unitsNode.Nodes)
    {
        var configName = node.Key;                    // 如: "1tnk"
        var chineseNames = node.Value.Nodes.Select(n => n.Key).ToList();  // 如: ["轻坦克", "轻坦"]
        
        // 建立映射关系
        configNameToChinese[configName] = chineseNames;
        foreach (var chineseName in chineseNames)
        {
            chineseToConfigName.TryAdd(chineseName, new List<string>());
            chineseToConfigName[chineseName].Add(configName);
        }
    }
}
```

#### 映射查询接口
```csharp
// 中文名 → 配置名
public static List<string> GetConfigNameByChinese(string chineseName)
{
    var ret = chineseToConfigName.TryGetValue(chineseName, out var configName) ? configName : null;
    if (ret == null)
    {
        Console.WriteLine($"未知单位: {chineseName}");
        return new List<string>();
    }
    return ret;
}

// 配置名 → 中文名
public static string GetChineseByConfigName(string configName)
{
    return configNameToChinese.TryGetValue(configName, out var chineseNames) ? 
           chineseNames.First() : configName;
}
```

### Copilot.yaml - 映射数据

包含了完整的中英文单位名称映射表。

#### 数据结构示例
```yaml
units:
    1tnk:                    # 游戏内部标识符
        轻坦克                # 中文名称
        轻坦                  # 中文简称
        轻型坦克              # 中文别名
        盟军轻坦              # 阵营描述
        light tank           # 英文名称
        lt                   # 英文简称
    
    powr:
        发电厂
        电厂
        小电
        小电厂
        基础电厂
        power plant
        basic power plant
        pp
        
nickname:                    # 单位集合别名
    士兵:                    # 表示所有士兵
        e1                   # 步兵
        e3                   # 火箭兵
    载具:                    # 表示所有载具
        1tnk                 # 轻坦克
        jeep                 # 吉普车
        harv                 # 采矿车
```

---

## 🎯 模组配置系统

### mod.yaml - Copilot 模组配置

定义了 Copilot 模组的完整配置，包括资源、规则、任务等。

#### 核心配置段
```yaml
Metadata:
    Title: Red Alert Copilot
    Version: {DEV_VERSION}
    WindowTitle: OpenRA - Red Alert Copilot

# 包含比赛专用规则
Rules:
    copilot|rules/world.yaml      # 世界规则
    copilot|rules/defaults.yaml   # 默认设置
    copilot|rules/vehicles.yaml   # 载具规则
    copilot|rules/structures.yaml # 建筑规则
    copilot|rules/infantry.yaml   # 步兵规则
    copilot|rules/aircraft.yaml   # 飞机规则

# 比赛任务配置
Missions:
    copilot|missions.yaml

# 服务器特性
ServerTraits:
    LobbyCommands
    SkirmishLogic
    PlayerPinger
    MasterServerPinger
```

#### 任务配置 (missions.yaml)
```yaml
AI word cup 2025 missions:
    copilot-01    # Mission-01 Basic Building
    copilot-02    # Mission-02 Fog of War  
    copilot-03    # Mission-03 Advanced Building
    copilot-04    # Mission-04 Air Strike
    copilot-05    # Mission-05 Basic Defense
```

---

## 🔗 集成机制分析

### 1. 启动流程

```mermaid
graph TD
    A[游戏启动] --> B[World 初始化]
    B --> C{WorldType == Regular?}
    C -->|是| D[创建 CopilotCommandServer]
    C -->|否| E[跳过 Copilot]
    D --> F[绑定端口 7445]
    F --> G[启动异步监听]
    G --> H[等待客户端连接]
    H --> I[ServerCommands.WorldLoaded]
    I --> J[注册命令处理器]
    J --> K[加载中文映射配置]
    K --> L[系统就绪]
```

### 2. 请求处理流程

```mermaid
graph TD
    A[客户端连接] --> B[接收 JSON 数据]
    B --> C[解析 MCPRequest]
    C --> D[验证请求格式]
    D --> E{验证通过?}
    E -->|否| F[返回错误响应]
    E -->|是| G[查找命令处理器]
    G --> H{找到处理器?}
    H -->|否| I[返回未知命令错误]
    H -->|是| J[执行命令处理器]
    J --> K[调用游戏内部 API]
    K --> L[返回成功响应]
    F --> M[关闭连接]
    I --> M
    L --> M
```

### 3. 命令执行机制

```mermaid
graph TD
    A[命令处理器] --> B[解析 JSON 参数]
    B --> C[中文名称转换]
    C --> D[查找游戏对象]
    D --> E[验证权限和状态]
    E --> F[创建 Order 对象]
    F --> G[world.IssueOrder]
    G --> H[游戏引擎执行]
    H --> I[返回执行结果]
```

### 4. 错误处理机制

- **多语言支持**: 根据请求语言返回中英文错误信息
- **参数验证**: 每个命令都有专门的参数验证逻辑
- **异常捕获**: 完整的 try-catch 机制防止服务器崩溃
- **调试模式**: 可选的详细日志输出用于开发调试

---

## 📊 性能和扩展性

### 性能特点
- **异步处理**: 使用 async/await 处理并发连接
- **内存管理**: 及时释放 Socket 连接和缓冲区
- **JSON 优化**: 使用 Newtonsoft.Json 高性能序列化

### 扩展机制
- **插件化命令**: 通过字典注册新命令处理器
- **模块化配置**: YAML 配置支持热重载
- **多语言支持**: 易于添加新语言支持

### 安全考虑
- **端口绑定**: 默认只监听本地端口
- **参数验证**: 严格的输入验证防止注入攻击
- **权限控制**: 基于游戏内权限系统

---

---

## 🔍 深度源码分析

### CopilotUtils.cs - 工具函数库

提供了一系列实用的工具函数，简化命令处理器的开发。

#### 核心工具函数
```csharp
public static class CopilotsUtils
{
    // 等待系统初始化完成
    public static void WaitInit()
    {
        // 确保所有必要的组件都已加载
        while (!IsSystemReady())
        {
            Thread.Sleep(100);
        }
    }

    // 解析目标参数
    public static List<Actor> ParseTargets(JToken targetsToken, World world)
    {
        var result = new List<Actor>();

        if (targetsToken is JArray targetsArray)
        {
            foreach (var target in targetsArray)
            {
                if (target.Type == JTokenType.Integer)
                {
                    // 按 ActorID 查找
                    var actorId = target.Value<int>();
                    var actor = world.GetActorById((uint)actorId);
                    if (actor != null) result.Add(actor);
                }
                else if (target.Type == JTokenType.String)
                {
                    // 按中文名称查找
                    var chineseName = target.Value<string>();
                    var configNames = CopilotsConfig.GetConfigNameByChinese(chineseName);
                    foreach (var configName in configNames)
                    {
                        result.AddRange(world.ActorsWithTrait<IPositionable>()
                            .Where(a => a.Actor.Info.Name == configName)
                            .Select(a => a.Actor));
                    }
                }
            }
        }

        return result;
    }

    // 解析位置参数
    public static CPos? ParseLocation(JObject json, World world)
    {
        var locationToken = json["location"];
        if (locationToken == null) return null;

        var x = locationToken["x"]?.Value<int>() ?? 0;
        var y = locationToken["y"]?.Value<int>() ?? 0;

        return new CPos(x, y);
    }

    // 获取玩家可见的所有单位
    public static IEnumerable<Actor> GetVisibleActors(World world, Player player)
    {
        return world.ActorsHavingTrait<IPositionable>()
            .Where(a => a.IsInWorld && !a.IsDead)
            .Where(a => player.CanViewActor(a));
    }
}
```

### 详细命令实现分析

#### 1. 生产管理命令
```csharp
JObject StartProductionCommand(JObject json, World world)
{
    var player = world.LocalPlayer;
    var units = json["units"]?.ToObject<string[]>() ?? new string[0];
    var autoPlaceBuilding = json["auto_place_building"]?.Value<bool>() ?? false;

    var results = new List<JObject>();

    foreach (var unitName in units)
    {
        // 中文名称转换
        var configNames = CopilotsConfig.GetConfigNameByChinese(unitName);
        if (!configNames.Any())
        {
            results.Add(new JObject
            {
                ["unit"] = unitName,
                ["status"] = "failed",
                ["error"] = $"未知单位: {unitName}"
            });
            continue;
        }

        var configName = configNames.First();

        // 查找合适的生产队列
        var queue = FindProductionQueue(player, configName);
        if (queue == null)
        {
            results.Add(new JObject
            {
                ["unit"] = unitName,
                ["status"] = "failed",
                ["error"] = "未找到合适的生产队列"
            });
            continue;
        }

        // 检查生产条件
        var buildableInfo = queue.BuildableItems().FirstOrDefault(b => b.Name == configName);
        if (buildableInfo == null)
        {
            results.Add(new JObject
            {
                ["unit"] = unitName,
                ["status"] = "failed",
                ["error"] = "当前无法生产此单位"
            });
            continue;
        }

        // 开始生产
        var order = new Order("StartProduction", queue.Actor, false)
        {
            TargetString = configName,
            ExtraData = autoPlaceBuilding ? 1u : 0u
        };

        world.IssueOrder(order);

        results.Add(new JObject
        {
            ["unit"] = unitName,
            ["config_name"] = configName,
            ["status"] = "success",
            ["queue_actor_id"] = queue.Actor.ActorID,
            ["estimated_time"] = buildableInfo.BuildTime
        });
    }

    return new JObject
    {
        ["results"] = new JArray(results),
        ["total_units"] = units.Length,
        ["successful_units"] = results.Count(r => r["status"].Value<string>() == "success")
    };
}
```

#### 2. 单位查询命令
```csharp
JObject ActorQueryCommand(JObject json, World world)
{
    var player = world.LocalPlayer;
    var targets = json["targets"];

    // 解析查询参数
    var queryParams = new
    {
        Type = targets["type"]?.ToObject<string[]>() ?? new string[0],
        Faction = targets["faction"]?.ToObject<string[]>() ?? new string[0],
        Range = targets["range"]?.Value<string>() ?? "all",
        Restrain = targets["restrain"]?.ToObject<JObject[]>() ?? new JObject[0]
    };

    // 获取基础单位集合
    var actors = GetActorsByRange(world, player, queryParams.Range);

    // 按类型过滤
    if (queryParams.Type.Any())
    {
        var configNames = new HashSet<string>();
        foreach (var typeName in queryParams.Type)
        {
            configNames.UnionWith(CopilotsConfig.GetConfigNameByChinese(typeName));
        }
        actors = actors.Where(a => configNames.Contains(a.Info.Name));
    }

    // 按阵营过滤
    if (queryParams.Faction.Any() && !queryParams.Faction.Contains("任意"))
    {
        actors = actors.Where(a => queryParams.Faction.Contains(GetFactionName(a.Owner)));
    }

    // 应用约束条件
    foreach (var restrain in queryParams.Restrain)
    {
        actors = ApplyRestrain(actors, restrain);
    }

    // 构建返回结果
    var result = actors.Select(actor => new JObject
    {
        ["actor_id"] = actor.ActorID,
        ["type"] = actor.Info.Name,
        ["chinese_name"] = CopilotsConfig.GetChineseByConfigName(actor.Info.Name),
        ["faction"] = GetFactionName(actor.Owner),
        ["position"] = new JObject
        {
            ["x"] = actor.CenterPosition.X / 1024,
            ["y"] = actor.CenterPosition.Y / 1024
        },
        ["health"] = GetActorHealth(actor),
        ["status"] = GetActorStatus(actor)
    }).ToArray();

    return new JObject
    {
        ["actors"] = new JArray(result),
        ["count"] = result.Length
    };
}

// 辅助函数：根据范围获取单位
IEnumerable<Actor> GetActorsByRange(World world, Player player, string range)
{
    switch (range.ToLower())
    {
        case "all":
            return world.ActorsHavingTrait<IPositionable>()
                .Where(a => a.IsInWorld && !a.IsDead);

        case "screen":
            var viewport = world.RenderPlayer?.Shroud.Bounds ?? world.Map.Bounds;
            return world.ActorsInBox(viewport)
                .Where(a => a.IsInWorld && !a.IsDead);

        case "selected":
            return world.Selection.Actors
                .Where(a => a.IsInWorld && !a.IsDead);

        default:
            return Enumerable.Empty<Actor>();
    }
}
```

#### 3. 地图查询命令
```csharp
JObject MapQueryCommand(JObject json, World world)
{
    var map = world.Map;
    var player = world.LocalPlayer;

    // 基础地图信息
    var mapInfo = new JObject
    {
        ["title"] = map.Title,
        ["size"] = new JObject
        {
            ["width"] = map.MapSize.X,
            ["height"] = map.MapSize.Y
        },
        ["bounds"] = new JObject
        {
            ["left"] = map.Bounds.Left,
            ["top"] = map.Bounds.Top,
            ["right"] = map.Bounds.Right,
            ["bottom"] = map.Bounds.Bottom
        },
        ["theater"] = map.Rules.TerrainInfo.Id
    };

    // 资源点信息
    var resources = new List<JObject>();
    foreach (var cell in map.AllCells)
    {
        var resourceType = map.Resources[cell];
        if (resourceType.Type != null && resourceType.Type != "")
        {
            resources.Add(new JObject
            {
                ["position"] = new JObject { ["x"] = cell.X, ["y"] = cell.Y },
                ["type"] = resourceType.Type,
                ["density"] = resourceType.Index
            });
        }
    }

    // 玩家出生点
    var spawnPoints = new List<JObject>();
    foreach (var spawn in map.SpawnPoints)
    {
        spawnPoints.Add(new JObject
        {
            ["position"] = new JObject { ["x"] = spawn.X, ["y"] = spawn.Y }
        });
    }

    return new JObject
    {
        ["map_info"] = mapInfo,
        ["resources"] = new JArray(resources),
        ["spawn_points"] = new JArray(spawnPoints),
        ["resource_count"] = resources.Count
    };
}
```

### 错误处理和调试系统

#### 多语言错误信息
```csharp
static readonly Dictionary<string, Dictionary<string, string>> ErrorMessages = new()
{
    ["INVALID_REQUEST"] = new()
    {
        ["zh"] = "无效的请求格式",
        ["en"] = "Invalid request format"
    },
    ["COMMAND_NOT_FOUND"] = new()
    {
        ["zh"] = "未知命令: {0}",
        ["en"] = "Unknown command: {0}"
    },
    ["PARAMETER_MISSING"] = new()
    {
        ["zh"] = "缺少必需参数: {0}",
        ["en"] = "Missing required parameter: {0}"
    },
    ["ACTOR_NOT_FOUND"] = new()
    {
        ["zh"] = "未找到指定单位",
        ["en"] = "Actor not found"
    },
    ["INSUFFICIENT_RESOURCES"] = new()
    {
        ["zh"] = "资源不足",
        ["en"] = "Insufficient resources"
    }
};
```

#### 调试模式输出
```csharp
void LogDebugInfo(string message, object data = null)
{
    if (!DebugMode) return;

    Console.WriteLine($"[Copilot Debug] {DateTime.Now:HH:mm:ss.fff} - {message}");
    if (data != null)
    {
        Console.WriteLine(JsonConvert.SerializeObject(data, Formatting.Indented));
    }
}
```

### 统计和监控系统

#### GameStatsTracker 集成
```csharp
public class CopilotStatsRecorder : IGameStatsRecorder
{
    private readonly Dictionary<string, int> commandCounts = new();
    private readonly Dictionary<string, int> queryCounts = new();

    public void RecordApiCall(string command, bool isQuery)
    {
        var dict = isQuery ? queryCounts : commandCounts;
        dict.TryGetValue(command, out var count);
        dict[command] = count + 1;

        // 记录到游戏统计系统
        Game.RunAfterTick(() =>
        {
            var statsTracker = world.WorldActor.TraitOrDefault<GameStatsTracker>();
            statsTracker?.LogEvent($"copilot_{(isQuery ? "query" : "command")}_{command}");
        });
    }

    public JObject GetStatistics()
    {
        return new JObject
        {
            ["commands"] = JObject.FromObject(commandCounts),
            ["queries"] = JObject.FromObject(queryCounts),
            ["total_calls"] = commandCounts.Values.Sum() + queryCounts.Values.Sum()
        };
    }
}
```

---

## 🎯 最佳实践和优化建议

### 1. 性能优化
- **缓存查询结果**: 对频繁查询的数据进行缓存
- **批量操作**: 支持批量命令减少网络开销
- **异步处理**: 长时间操作使用异步模式

### 2. 错误处理
- **参数验证**: 严格验证所有输入参数
- **异常捕获**: 防止单个命令错误影响整个服务
- **友好提示**: 提供详细的错误信息和建议

### 3. 扩展开发
- **命令模板**: 使用统一的命令处理模板
- **配置驱动**: 通过配置文件控制功能开关
- **插件机制**: 支持动态加载新的命令处理器

### 4. 调试技巧
- **日志分级**: 区分不同级别的调试信息
- **请求追踪**: 为每个请求分配唯一ID
- **性能监控**: 记录命令执行时间和资源使用

---

## 📚 开发者指南

### 添加新命令的步骤

1. **定义命令处理器**
```csharp
string MyNewCommand(JObject json, World world)
{
    // 1. 参数解析和验证
    var param1 = json["param1"]?.Value<string>();
    if (string.IsNullOrEmpty(param1))
        return "参数 param1 不能为空";

    // 2. 业务逻辑处理
    // ... 具体实现 ...

    // 3. 返回结果
    return "命令执行成功";
}
```

2. **注册命令处理器**
```csharp
public void WorldLoaded(World w, WorldRenderer wr)
{
    // 在现有注册代码中添加
    w.CopilotServer.CommandHandlers["my_new_command"] = MyNewCommand;
}
```

3. **添加参数验证**
```csharp
public static (bool isValid, MCPError error) ValidateMyNewCommandParams(JObject parameters)
{
    if (parameters["param1"] == null)
        return (false, new MCPError { Code = "PARAMETER_MISSING", Message = "缺少参数 param1" });

    return (true, null);
}
```

4. **更新中文映射** (如需要)
```yaml
# 在 Copilot.yaml 中添加新的映射
units:
    new_unit:
        新单位
        新建单位
        new unit
```

### 测试和调试

1. **启用调试模式**
```bash
# 在游戏设置中启用
CopilotDebug = true
```

2. **使用测试客户端**
```python
import socket
import json

def test_command(command, params):
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.connect(('localhost', 7445))

    request = {
        "apiVersion": "1.0",
        "requestId": "test-001",
        "command": command,
        "params": params,
        "language": "zh"
    }

    sock.send(json.dumps(request).encode('utf-8'))
    response = sock.recv(4096).decode('utf-8')
    sock.close()

    return json.loads(response)
```

---

*深度源码解读完成 - 2025年8月*
