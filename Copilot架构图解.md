# OpenRA Copilot 架构图解

本文档通过图表和流程图直观展示 OpenRA Copilot 的系统架构和工作原理。

## 🏗️ 系统整体架构

```mermaid
graph TB
    subgraph "外部 AI Agent"
        A[Python Client]
        B[MCP Server]
        C[其他 AI 框架]
    end
    
    subgraph "网络通信层"
        D[Socket TCP:7445]
        E[JSON Protocol]
    end
    
    subgraph "OpenRA Copilot 核心"
        F[CopilotCommandServer]
        G[命令路由器]
        H[参数验证器]
        I[错误处理器]
    end
    
    subgraph "命令处理层"
        J[ServerCommands]
        K[CommandHandlers]
        L[QueryHandlers]
    end
    
    subgraph "配置和映射"
        M[CopilotConfig]
        N[中文单位映射]
        O[Copilot.yaml]
    end
    
    subgraph "OpenRA 游戏引擎"
        P[World]
        Q[Actor System]
        R[Order System]
        S[Game Logic]
    end
    
    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    G --> J
    J --> K
    J --> L
    K --> M
    L --> M
    M --> N
    N --> O
    K --> P
    L --> P
    P --> Q
    P --> R
    Q --> S
    R --> S
```

## 🔄 请求处理流程

```mermaid
sequenceDiagram
    participant AI as AI Agent
    participant Socket as Socket Server
    participant Router as Command Router
    participant Handler as Command Handler
    participant Config as Config System
    participant Game as Game Engine
    
    AI->>Socket: JSON Request
    Socket->>Socket: Parse JSON
    Socket->>Router: Route Command
    Router->>Router: Validate Request
    
    alt 验证失败
        Router->>Socket: Error Response
        Socket->>AI: JSON Error
    else 验证成功
        Router->>Handler: Execute Command
        Handler->>Config: 中文名称转换
        Config->>Handler: 返回配置名
        Handler->>Game: 调用游戏API
        Game->>Handler: 返回结果
        Handler->>Socket: Success Response
        Socket->>AI: JSON Response
    end
```

## 📊 核心组件关系图

```mermaid
classDiagram
    class CopilotCommandServer {
        +Socket serverSocket
        +World world
        +Dictionary CommandHandlers
        +Dictionary QueryHandlers
        +Start()
        +HandleClient()
        +SendResponse()
    }
    
    class ServerCommands {
        +MoveActorCommand()
        +AttackCommand()
        +QueryActorCommand()
        +PlayerBaseInfoQuery()
        +WorldLoaded()
    }
    
    class CopilotConfig {
        +Dictionary configNameToChinese
        +Dictionary chineseToConfigName
        +LoadConfig()
        +GetConfigNameByChinese()
        +GetChineseByConfigName()
    }
    
    class MCPRequest {
        +string ApiVersion
        +string RequestId
        +string Command
        +JObject Params
        +string Language
    }
    
    class MCPResponse {
        +int Status
        +string RequestId
        +string Response
        +JObject Data
        +MCPError Error
    }
    
    class World {
        +CopilotCommandServer CopilotServer
        +ActorMap ActorMap
        +IssueOrder()
        +GetActorById()
    }
    
    CopilotCommandServer --> ServerCommands : uses
    CopilotCommandServer --> World : references
    ServerCommands --> CopilotConfig : uses
    CopilotCommandServer --> MCPRequest : processes
    CopilotCommandServer --> MCPResponse : generates
    World --> CopilotCommandServer : contains
```

## 🎮 命令执行流程

```mermaid
flowchart TD
    A[接收命令] --> B{命令类型}
    
    B -->|move_actor| C[移动单位]
    B -->|attack| D[攻击目标]
    B -->|query_actor| E[查询单位]
    B -->|place_building| F[放置建筑]
    
    C --> G[解析目标参数]
    D --> G
    E --> H[解析查询参数]
    F --> I[解析建筑参数]
    
    G --> J[中文名称转换]
    H --> J
    I --> J
    
    J --> K[查找游戏对象]
    K --> L{对象存在?}
    
    L -->|否| M[返回错误]
    L -->|是| N[验证权限]
    
    N --> O{权限通过?}
    O -->|否| M
    O -->|是| P[创建Order]
    
    P --> Q[world.IssueOrder]
    Q --> R[游戏引擎执行]
    R --> S[返回成功结果]
    
    M --> T[发送响应]
    S --> T
```

## 🌐 网络通信协议

```mermaid
graph LR
    subgraph "请求格式"
        A["{<br/>apiVersion: '1.0',<br/>requestId: 'uuid',<br/>command: 'move_actor',<br/>params: {...},<br/>language: 'zh'<br/>}"]
    end
    
    subgraph "Socket 传输"
        B[TCP Socket<br/>Port: 7445<br/>UTF-8 JSON]
    end
    
    subgraph "响应格式"
        C["{<br/>status: 1,<br/>requestId: 'uuid',<br/>response: '成功',<br/>data: {...}<br/>}"]
        D["{<br/>status: -1,<br/>requestId: 'uuid',<br/>error: {<br/>  code: 'ERROR_CODE',<br/>  message: '错误信息'<br/>}<br/>}"]
    end
    
    A --> B
    B --> C
    B --> D
```

## 🗂️ 配置文件结构

```mermaid
graph TD
    subgraph "Copilot.yaml"
        A[units:<br/>单位映射]
        B[nickname:<br/>别名映射]
        C[buildings:<br/>建筑映射]
    end
    
    subgraph "mod.yaml"
        D[Metadata:<br/>模组信息]
        E[Rules:<br/>游戏规则]
        F[Missions:<br/>任务配置]
    end
    
    subgraph "missions.yaml"
        G[copilot-01:<br/>基础建造]
        H[copilot-02:<br/>战争迷雾]
        I[copilot-03:<br/>高级建造]
    end
    
    A --> J[CopilotConfig.LoadConfig]
    B --> J
    C --> J
    
    D --> K[ModData.LoadMod]
    E --> K
    F --> K
    
    G --> L[Mission System]
    H --> L
    I --> L
```

## 🔧 开发扩展流程

```mermaid
flowchart TD
    A[需求分析] --> B[设计命令接口]
    B --> C[实现命令处理器]
    C --> D[添加参数验证]
    D --> E[注册命令处理器]
    E --> F[更新中文映射]
    F --> G[编写测试用例]
    G --> H[调试和优化]
    H --> I[文档更新]
    
    subgraph "代码实现"
        C1[string MyCommand(JObject json, World world)]
        C2[参数解析]
        C3[业务逻辑]
        C4[返回结果]
    end
    
    subgraph "注册机制"
        E1[w.CopilotServer.CommandHandlers]
        E2[w.CopilotServer.QueryHandlers]
    end
    
    C --> C1
    C1 --> C2
    C2 --> C3
    C3 --> C4
    
    E --> E1
    E --> E2
```

## 📈 性能监控架构

```mermaid
graph TB
    subgraph "监控指标"
        A[API调用次数]
        B[响应时间]
        C[错误率]
        D[并发连接数]
    end
    
    subgraph "数据收集"
        E[CopilotStatsRecorder]
        F[GameStatsTracker]
        G[调试日志]
    end
    
    subgraph "数据存储"
        H[内存统计]
        I[日志文件]
        J[游戏统计系统]
    end
    
    A --> E
    B --> E
    C --> E
    D --> E
    
    E --> F
    E --> G
    
    F --> H
    F --> I
    G --> I
    F --> J
```

## 🛡️ 安全和错误处理

```mermaid
graph TD
    A[客户端请求] --> B[Socket接收]
    B --> C[JSON解析]
    C --> D{解析成功?}
    
    D -->|否| E[JSON格式错误]
    D -->|是| F[请求验证]
    
    F --> G{验证通过?}
    G -->|否| H[参数错误]
    G -->|是| I[命令执行]
    
    I --> J{执行成功?}
    J -->|否| K[执行错误]
    J -->|是| L[成功响应]
    
    E --> M[错误响应]
    H --> M
    K --> M
    L --> N[正常响应]
    
    M --> O[记录错误日志]
    N --> P[记录访问日志]
    
    O --> Q[关闭连接]
    P --> Q
```

---

## 📚 图表说明

### 系统架构图
- 展示了从外部AI Agent到OpenRA游戏引擎的完整数据流
- 突出了各个组件之间的依赖关系
- 清晰地分离了不同的功能层次

### 请求处理流程
- 详细描述了一个API请求的完整生命周期
- 包含了错误处理的分支逻辑
- 展示了各组件之间的交互时序

### 核心组件关系图
- 使用UML类图展示主要类的结构和关系
- 突出了关键的属性和方法
- 清晰地表达了组件之间的依赖关系

### 命令执行流程
- 展示了不同类型命令的处理路径
- 包含了完整的验证和错误处理逻辑
- 突出了中文名称转换的重要性

这些图表为理解OpenRA Copilot的架构和工作原理提供了直观的视觉参考，有助于开发者快速掌握系统的核心概念和实现细节。

---

*架构图解完成 - 2025年8月*
