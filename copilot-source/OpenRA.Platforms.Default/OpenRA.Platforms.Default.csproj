﻿<Project Sdk="Microsoft.NET.Sdk">
  <ItemGroup>
    <ProjectReference Include="..\OpenRA.Game\OpenRA.Game.csproj" />
    <PackageReference Include="OpenRA-Freetype6" Version="1.0.11" />
    <PackageReference Include="OpenRA-OpenAL-CS" Version="1.0.22" />
    <PackageReference Include="OpenRA-SDL2-CS" Version="1.0.40" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="OpenRA.Platforms.Default.dll.config" Condition="'$(TargetPlatform)' != 'win-x64' And '$(TargetPlatform)' != 'win-x86'">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
</Project>
