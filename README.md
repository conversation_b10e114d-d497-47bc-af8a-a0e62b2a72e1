# AI红警世界杯 2025 - Code Alert AI Agent

## 比赛概述

本项目参与 AI红警世界杯 2025 比赛，开发一个能够接收自然语言指令并在红警游戏中完成任务的 AI Agent。

## 🚀 快速开始

### 方式一：启动比赛版本（推荐）
```bash
./start-game.sh
```
- ✅ 开箱即用，无需编译
- ✅ 启动速度快

### 方式二：从源码启动（开发）
```bash
./start-from-source.sh
```
- ✅ 自动检测编译状态
- ✅ 支持源码修改
- ✅ 一键编译并启动

### 更新所有组件
```bash
./update.sh
```

## 比赛形式

### 初赛阶段（线上进行）
- **时间**: 8月4日 - 9月1日
- **指令输入**: 文本方式传递给 Agent（欢迎尝试语音输入但不作为加分项）
- **任务**: 开发一个 AI Agent，在 20 个关卡中完成任务目标
- **评分**: 每完成一个关卡，游戏生成加密日志，上传至官网评分，实时公布排行榜

### 关卡挑战
1. 基础单位建造
2. 地图探索
3. 基地防守
4. 多单位协调作战

### 决赛阶段 - AI红警世界杯现场对战
- **地点**: 杭州 · GOSIM China 大会现场
- **时间**: 9月13日 - 9月14日
- **语音指挥**: 玩家通过语音输入指令，指挥 AI Agent 作战
- **技术支持**: 组委会提供本地部署的 Moxin-7B 模型，实现语音转文字支持
- **赛制**: 1对1对战、单循环制，胜率最高者夺冠
- **直播**: 大屏幕现场展示，网络直播

### 观众挑战赛
GOSIM大会期间，安排观众挑战赛，邀请红警资深玩家与AI参赛者同台竞技。

## 游戏引擎与开发接口

### 基于 OpenRA
- 基于开源项目 OpenRA（GitHub 15,700+ Stars）
- 支持 Windows 和 macOS 平台（Linux暂不支持）
- 引入现代化机制的经典红警游戏重构

### 开发接口
组委会在 OpenRA 基础上开发了具备开放接口的游戏引擎，支持：
- Socket 接口
- C# 接口
- Python 接口
- RESTful API
- MCP Client

参赛者可自由选择擅长的语言与接口构建 AI Agent。

## 相关链接
- **大赛官网**: https://hackathon.scimigo.com/
- **OpenRA GitHub**: https://github.com/OpenRA/OpenRA
- **比赛引擎和关卡**: https://github.com/OpenCodeAlert/Hackathon2025.git
- **比赛资源下载**: https://github.com/OpenCodeAlert/Hackathon2025/releases

## 本机环境
- **设备**: Mac Studio (2023) - Mac14,14
- **芯片**: Apple M2 Ultra (24核心)
- **内存**: 192 GB 统一内存
- **存储**: 477 GB SSD + 多个外接硬盘
- **系统**: macOS 15.5 (Sequoia) ARM64

## 快速开始

### 比赛版本（推荐）

运行以下命令下载和更新比赛引擎和代码：

```bash
# 下载比赛引擎和代码
./update.sh

# 启动比赛版本游戏
./start-game.sh
```

### 官方开发版本

如果需要查看源码或进行开发调试，可以手动使用官方的编译和启动命令：

#### 1. 编译官方源码 ✅

```bash
# 进入官方源码目录
cd official-openra

# 使用官方 Makefile 编译（已测试成功）
make

# 或者使用 dotnet 直接编译
dotnet build OpenRA.sln --configuration Release
```

**编译结果：**
- ✅ 编译成功，生成了所有必要的 .dll 和可执行文件
- ✅ 生成的文件位于 `bin/` 目录
- ✅ 包含游戏引擎、模组和服务器组件

#### 2. 安装 .NET 8.0 运行时 ✅

官方源码需要 .NET 8.0 运行时，如果系统只有 .NET 9.0，需要安装 .NET 8.0：

```bash
# 清理可能损坏的用户 .NET 安装
rm -rf ~/.dotnet

# 安装 .NET 8.0 运行时
curl -sSL https://dot.net/v1/dotnet-install.sh | bash /dev/stdin --channel 8.0 --runtime dotnet

# 验证安装
export PATH="$HOME/.dotnet:$PATH"
export DOTNET_ROOT="$HOME/.dotnet"
dotnet --list-runtimes
```

**预期输出：**
```
Microsoft.NETCore.App 8.0.19 [/Users/<USER>/.dotnet/shared/Microsoft.NETCore.App]
```

#### 3. 启动游戏 ✅

**成功的启动方法：**

```bash
# 进入官方源码目录
cd official-openra

# 设置 .NET 8.0 环境变量
export PATH="$HOME/.dotnet:$PATH"
export DOTNET_ROOT="$HOME/.dotnet"

# 方法一：使用官方启动脚本（推荐）
./launch-game.sh Game.Mod=ra

# 方法二：直接使用可执行文件
./bin/OpenRA Game.Mod=ra

# 方法三：使用 dotnet 命令
dotnet bin/OpenRA.dll Engine.EngineDir=".." Game.Mod=ra
```

**可用的模组：**
- `ra` - 红色警戒
- `cnc` - 泰伯利亚黎明
- `d2k` - 沙丘2000
- `ts` - 泰伯利亚之日

#### 4. 专用服务器 ✅

```bash
# 设置环境变量
export PATH="$HOME/.dotnet:$PATH"
export DOTNET_ROOT="$HOME/.dotnet"

# 启动服务器
./launch-dedicated.sh

# 或指定模组启动服务器
Mod="ra" ./launch-dedicated.sh
```

#### 5. 重要发现

- ✅ **官方源码可以正常启动** - 解决了 .NET 8.0 运行时问题
- ✅ **检测到比赛版本模组** - 官方源码能识别 `copilot-v1.0.0` 模组
- ✅ **图形渲染正常** - 使用 Apple M2 Ultra 的 OpenGL 4.1 Metal 渲染
- ⚠️ **需要正确的环境变量** - 必须设置 `PATH` 和 `DOTNET_ROOT`

## 脚本说明

### 启动脚本

项目提供了简洁的启动脚本：

#### 1. 比赛版本启动脚本

**`start-game.sh`** - 启动预编译的比赛版本
```bash
./start-game.sh
```
- ✅ 开箱即用，无需编译
- ✅ 启动速度快
- ✅ 稳定可靠
- ❌ 无法修改源码

#### 2. 源码一键启动脚本 🆕

**`start-from-source.sh`** - 一键编译并启动
```bash
./start-from-source.sh
```
- ✅ 自动检测是否需要编译
- ✅ 没有编译就自动编译
- ✅ 支持源码修改和开发
- ✅ 自动检测 .NET/Mono 环境
- ✅ 直接启动 Copilot mod

**命令行参数支持：**
```bash
# 基本启动
./start-from-source.sh

# 指定任务启动
./start-from-source.sh Mission=copilot-01

# 调试模式
./start-from-source.sh Engine.DebugMode=true

# 窗口模式
./start-from-source.sh Graphics.Mode=Windowed
```

#### 3. 项目更新脚本

**`update.sh`** - 更新所有组件
```bash
./update.sh
```
- ✅ 更新游戏引擎
- ✅ 更新比赛文档
- ✅ 更新源码
- ✅ 检查系统依赖

### 环境要求

| 组件 | 比赛版本 | 官方版本 | 说明 |
|------|----------|----------|------|
| .NET 运行时 | 无需安装 | .NET 8.0 必需 | 比赛版本自带运行时 |
| 系统依赖 | 无 | SDL2, OpenGL | 官方版本需要图形库 |
| 安装复杂度 | 简单 | 中等 | 比赛版本开箱即用 |
| 开发调试 | 不支持 | 完全支持 | 官方版本可查看源码 |

## 问题解决记录

### 1. 删除标准版 OpenRA ✅
- **问题**：系统中安装了标准版 OpenRA（release-20250330）
- **解决**：删除标准版应用，保留比赛版本
- **命令**：
  ```bash
  rm -rf "/Applications/OpenRA - Red Alert.app"
  rm -rf "/Applications/OpenRA - Tiberian Dawn.app"
  rm -rf "/Applications/OpenRA - Dune 2000.app"
  ```
- **结果**：只保留 `/Applications/OpenRA - Copilot.app` (v1.0.0)

### 2. 解决官方源码启动问题 ✅
- **问题**：官方源码需要 .NET 8.0，系统只有 .NET 9.0
- **解决**：安装用户级 .NET 8.0 运行时
- **命令**：
  ```bash
  # 清理损坏的安装
  rm -rf ~/.dotnet

  # 重新安装 .NET 8.0 运行时
  curl -sSL https://dot.net/v1/dotnet-install.sh | bash /dev/stdin --channel 8.0 --runtime dotnet

  # 设置环境变量
  export PATH="$HOME/.dotnet:$PATH"
  export DOTNET_ROOT="$HOME/.dotnet"
  ```
- **结果**：官方源码可以正常启动，检测到比赛版本模组

### 3. 最终状态 ✅
- **比赛版本**：开箱即用，无需配置
- **官方版本**：需要 .NET 8.0 环境变量，但可以正常启动
- **模组兼容**：官方版本能识别比赛版本的 `copilot-v1.0.0` 模组

## 比赛版本 vs 官方版本关系详解

### 🎯 比赛版本（OpenRA Copilot）
**本质**：基于 OpenRA 的**定制化分支**，专为 AI红警世界杯 2025 比赛设计

**特点**：
- ✅ **完整的游戏引擎**：不是模组，是独立的游戏程序
- ✅ **内置比赛模组**：包含 `copilot` 模组，专为比赛优化
- ✅ **中文支持**：内置中文单位名称映射（如"轻坦克"→"1tnk"）
- ✅ **API 接口**：支持 Socket API 控制游戏（CopilotCommandServer）
- ✅ **简化游戏**：保留核心单位，移除复杂机制
- ✅ **自包含**：内置 .NET 6.0 运行时，无需额外安装

**源码**：✅ **有源码**！位于 `https://github.com/OpenCodeAlert/OpenCodeAlert` (Hackathon 分支)

### 🔧 官方版本（OpenRA）
**本质**：OpenRA 开源项目的**原始源码**

**特点**：
- ✅ **完整源码**：可以查看、修改所有代码
- ✅ **标准模组**：包含 ra、cnc、d2k、ts 等标准模组
- ✅ **开发工具**：支持模组开发、地图编辑
- ✅ **最新功能**：包含最新的游戏特性和修复
- ⚠️ **需要配置**：需要 .NET 8.0 运行时和环境变量

**用途**：源码研究、模组开发、功能扩展

### 🔗 两者关系

1. **技术关系**：
   - 比赛版本是 OpenRA 官方仓库的 **fork**：`OpenCodeAlert/OpenCodeAlert`
   - 基于 OpenRA 的 **Hackathon 分支**开发
   - 比赛版本 = OpenRA 核心引擎 + 比赛专用模组 + API 接口 + 中文支持
   - 官方版本可以**识别**比赛版本的模组（copilot-v1.0.0）

2. **源码结构**：
   - 比赛版本源码：`copilot-source/` (OpenCodeAlert/OpenCodeAlert)
   - 官方版本源码：`official-openra/` (OpenRA/OpenRA)
   - 比赛版本新增：`CopilotCommandServer.cs`、`CopilotModels.cs`、`mods/copilot/`

3. **版本差异**：
   - 比赛版本：.NET 6.0 + Socket API + 中文支持 + 比赛优化
   - 官方版本：.NET 8.0 + 完整功能 + 最新特性

4. **数据兼容**：
   - 比赛版本的地图、存档可能与官方版本兼容
   - 两个版本可以共存，不会冲突

### 📊 功能对比

| 功能 | 比赛版本 | 官方版本 |
|------|----------|----------|
| **游戏体验** | 简化版红警 | 完整版红警 |
| **AI 接口** | ✅ Socket API | ❌ 无内置 API |
| **中文支持** | ✅ 内置中文映射 | ❌ 英文为主 |
| **源码访问** | ✅ 开源 (Hackathon 分支) | ✅ 完全开源 |
| **模组开发** | ⚠️ 比赛专用模组 | ✅ 完全支持 |
| **安装难度** | ✅ 开箱即用 | ⚠️ 需要配置 |
| **比赛专用** | ✅ 专为比赛设计 | ❌ 通用版本 |
| **仓库地址** | OpenCodeAlert/OpenCodeAlert | OpenRA/OpenRA |

## 源码仓库

### 📁 项目结构
```
├── engines/                   # 比赛版本二进制程序
├── hackathon-code/           # 比赛文档和示例（完整源码）
├── official-openra/          # OpenRA 官方源码（完整源码）
└── copilot-source/           # 比赛版本源码（完整源码）
    ├── bin/                  # 编译后的程序文件
    ├── mods/copilot/         # copilot 模组源码
    ├── mods/common/Copilot.yaml  # 中文单位映射
    └── OpenRA.Game/CopilotCommandServer.cs  # Socket API 服务器
```

### 🔗 原始仓库地址（参考）
- **比赛版本源码**：https://github.com/OpenCodeAlert/OpenCodeAlert (Hackathon 分支)
- **官方版本源码**：https://github.com/OpenRA/OpenRA (bleed 分支)
- **比赛文档代码**：https://github.com/OpenCodeAlert/Hackathon2025

**注意**：现在所有源码都直接包含在主仓库中，不再作为子模块管理。

### 📊 三个版本完整对比

| 特性 | 比赛版本二进制 | 比赛版本源码 | 官方版本源码 |
|------|---------------|-------------|-------------|
| **安装难度** | ✅ 开箱即用 | ⚠️ 需编译 | ⚠️ 需编译+配置 |
| **运行时** | 内置 .NET 6.0 | 需 .NET 6.0 SDK | 需 .NET 8.0 运行时 |
| **源码访问** | ❌ 无 | ✅ 完整源码 | ✅ 完整源码 |
| **Socket API** | ✅ 内置 | ✅ 可查看实现 | ❌ 无 |
| **中文支持** | ✅ 完整 | ✅ 完整 | ❌ 无 |
| **copilot 模组** | ✅ 内置 | ✅ 源码可见 | ❌ 无 |
| **比赛地图** | ✅ 5个地图 | ✅ 5个地图 | ❌ 无 |
| **调试能力** | ❌ 无 | ✅ 完整调试 | ✅ 完整调试 |
| **模组开发** | ❌ 不支持 | ⚠️ 比赛专用 | ✅ 完全支持 |
| **用途** | 参赛使用 | 源码研究+参赛 | 游戏开发研究 |

## 从比赛版本源码启动游戏 ✅

### 1. 安装 .NET 6.0 SDK

比赛版本源码需要 .NET 6.0 SDK 进行编译：

```bash
# 安装 .NET 6.0 SDK
curl -sSL https://dot.net/v1/dotnet-install.sh | bash /dev/stdin --channel 6.0

# 设置环境变量
export PATH="$HOME/.dotnet:$PATH"
export DOTNET_ROOT="$HOME/.dotnet"

# 验证安装
dotnet --list-sdks
```

**预期输出：**
```
6.0.428 [/Users/<USER>/.dotnet/sdk]
```

### 2. 编译比赛版本源码

```bash
# 进入比赛版本源码目录
cd copilot-source

# 设置环境变量
export PATH="$HOME/.dotnet:$PATH"
export DOTNET_ROOT="$HOME/.dotnet"

# 编译源码
make
```

**编译成功标志：**
```
Build succeeded.
    1 Warning(s)
    0 Error(s)
Time Elapsed 00:00:04.05
```

### 3. 启动比赛版本游戏

```bash
# 启动 copilot 模组游戏
./launch-game.sh Game.Mod=copilot

# 启动专用服务器
./launch-dedicated.sh
```
export PATH="$HOME/.dotnet:$PATH" && export DOTNET_ROOT="$HOME/.dotnet" && ./launch-game.sh Game.Mod=copilot
cd copilot-source && export PATH="$HOME/.dotnet:$PATH" && export DOTNET_ROOT="$HOME/.dotnet" && ./launch-game.sh Game.Mod=copilot

**启动成功标志：**
```
=Platform is OSX (Arm64)
Engine version is {DEV_VERSION}
Runtime: .NET CLR 6.0.36
Loading mod: copilot
警告：无法找到玩家 ''，统计跟踪已禁用
```

### 4. 在游戏中查看比赛任务

启动游戏后，按以下步骤查看5个比赛任务：

1. **主菜单** → **Missions** (任务)
2. 找到 **"AI红警世界杯 2025 比赛任务"** 分类
3. 选择任意一个比赛任务：
   - **Mission-01 Basic Building** (基础建造)
   - **Mission-02 Fog of War** (战争迷雾)
   - **Mission-03 Advanced Building** (高级建造)
   - **Mission-04 Air Strike** (空中打击)
   - **Mission-05 Basic Defense** (基础防御)

**注意**：这些任务只在使用 `copilot` 模组时可见，需要确保启动命令是：
```bash
./launch-game.sh Game.Mod=copilot
```

### 5. 关键发现

- ✅ **成功编译**：使用 .NET 6.0 SDK 编译成功
- ✅ **成功启动**：copilot 模组可以正常启动
- ✅ **中文支持**：显示中文警告信息
- ✅ **比赛任务**：5个专用比赛任务已配置并可在任务选择器中访问
- ✅ **服务器功能**：专用服务器可以正常启动
- ✅ **模组检测**：能识别内部和外部的 copilot 模组版本

## 去子模块化管理 ✅

### 🎯 新的管理方式

**重要变更**：项目已从子模块管理改为**直接包含所有源码**，这样更简单直接！

### 优点：
- ✅ **完全控制**：所有代码都在主仓库中，可以任意修改
- ✅ **简单管理**：不需要处理子模块的复杂性
- ✅ **版本一致**：所有代码使用同一个提交历史
- ✅ **恢复简单**：通过 git.sanyitec.cc 的历史记录恢复任何版本

### 克隆项目

```bash
# 简单克隆，包含所有源码
git clone https://git.sanyitec.cc/sanyitec/code-alert-ai-agent.git
cd code-alert-ai-agent
```

### 更新上游代码（手动）

如果需要获取原仓库的最新更新：

```bash
# 1. 备份当前修改
git add . && git commit -m "Backup local changes"

# 2. 手动合并上游更改（需要时）
# 进入对应目录，手动下载最新代码并合并

# 3. 提交合并结果
git add . && git commit -m "Merge upstream changes"
```

## 推荐使用方式

- **🏆 参赛开发**：使用比赛版本二进制（`./update.sh` + `./start-game.sh`）
- **🔍 比赛源码研究**：编译并运行 `copilot-source/`，重点关注 `CopilotCommandServer.cs`
- **🔍 官方源码研究**：使用官方版本（`cd official-openra` + 设置环境变量 + `./launch-game.sh`）
- **🖥️ 服务器部署**：使用比赛版本或官方版本的专用服务器功能
- **🎮 模组开发**：使用官方版本进行模组和地图制作
- **🔄 保持更新**：定期运行 `./sync-submodules.sh` 同步最新代码
